import React, { useState } from 'react';
import { Link } from 'react-router-dom';

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Header / Navbar */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <h1 className="text-2xl font-bold text-gray-900">Nextspace</h1>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8">
              <Link to="/" className="text-gray-700 hover:text-green-700 px-3 py-2 text-sm font-medium">
                Home
              </Link>
              <Link to="/about" className="text-gray-700 hover:text-green-700 px-3 py-2 text-sm font-medium">
                About
              </Link>
              <a href="#projects" className="text-gray-700 hover:text-green-700 px-3 py-2 text-sm font-medium">
                Projects
              </a>
              <a href="#contact" className="text-gray-700 hover:text-green-700 px-3 py-2 text-sm font-medium">
                Contact
              </a>
            </nav>

            {/* CTA Button */}
            <div className="hidden md:flex">
              <button className="border-2 border-green-700 text-green-700 hover:bg-green-700 hover:text-white px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                Get This Template
              </button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-700 hover:text-green-700 focus:outline-none focus:text-green-700"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden">
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <Link to="/" className="text-gray-700 hover:text-green-700 block px-3 py-2 text-base font-medium">
                  Home
                </Link>
                <Link to="/about" className="text-gray-700 hover:text-green-700 block px-3 py-2 text-base font-medium">
                  About
                </Link>
                <a href="#projects" className="text-gray-700 hover:text-green-700 block px-3 py-2 text-base font-medium">
                  Projects
                </a>
                <a href="#contact" className="text-gray-700 hover:text-green-700 block px-3 py-2 text-base font-medium">
                  Contact
                </a>
                <button className="w-full mt-2 border-2 border-green-700 text-green-700 hover:bg-green-700 hover:text-white px-6 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                  Get This Template
                </button>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 via-stone-50 to-gray-100 overflow-hidden">
        {/* Abstract circular patterns */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-green-200 to-green-300 rounded-full opacity-40"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-emerald-200 to-teal-300 rounded-full opacity-50"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-br from-stone-200 to-gray-300 rounded-full opacity-30"></div>
          <div className="absolute top-1/2 right-1/3 w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-200 rounded-full opacity-35"></div>
          <div className="absolute bottom-40 right-10 w-28 h-28 bg-gradient-to-br from-teal-100 to-green-200 rounded-full opacity-25"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left content */}
            <div className="text-center lg:text-left">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Elevating Spaces, Crafting Dreams
              </h1>
              <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-lg mx-auto lg:mx-0">
                Transforming your vision into reality with expert interior design and architectural excellence.
              </p>
              <button className="bg-green-700 hover:bg-green-800 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg">
                Book an appointment
              </button>
            </div>

            {/* Right content - Hero image */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <img
                  src="/src/assets/hero.png"
                  alt="Cozy living room interior"
                  className="w-full h-96 object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            {/* Left content */}
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                About Us – Architecture Interior.
              </h2>
            </div>

            {/* Right content */}
            <div>
              <p className="text-lg text-gray-600 leading-relaxed">
                We are passionate about creating spaces that inspire and transform. With years of experience in interior design and architecture,
                we bring your vision to life through innovative design solutions and meticulous attention to detail.
              </p>
            </div>
          </div>

          {/* Three interior images */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <img
                src="/src/assets/hero1.png"
                alt="Modern living space"
                className="w-full h-64 object-cover"
              />
            </div>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <img
                src="/src/assets/hero.png"
                alt="Elegant bedroom design"
                className="w-full h-64 object-cover"
              />
            </div>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <img
                src="/src/assets/hero1.png"
                alt="Contemporary kitchen"
                className="w-full h-64 object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">8k</div>
              <div className="text-gray-600 font-medium">Interior Projects</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">31</div>
              <div className="text-gray-600 font-medium">Years of Works</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">12k</div>
              <div className="text-gray-600 font-medium">Satisfied Clients</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold text-green-700 mb-2">97%</div>
              <div className="text-gray-600 font-medium">Happy Rate</div>
            </div>
          </div>
        </div>
      </section>

      {/* Client Stories Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left content - Testimonial */}
            <div className="bg-green-700 text-white p-12 rounded-2xl">
              <h2 className="text-3xl font-bold mb-6">Client Stories</h2>
              <blockquote className="text-lg leading-relaxed mb-6">
                "Working with Nextspace was an incredible experience. They transformed our house into a dream home that perfectly reflects our style and needs. The attention to detail and professionalism was outstanding."
              </blockquote>
              <div>
                <div className="font-semibold">Sarah Johnson</div>
                <div className="text-green-200">Homeowner & Interior Design Enthusiast</div>
              </div>
            </div>

            {/* Right content - Lifestyle image */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
                <img
                  src="/src/assets/hero.png"
                  alt="Warm wooden interior design"
                  className="w-full h-96 object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Get your dream home with expert help.
          </h2>
          <button className="bg-green-700 hover:bg-green-800 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-200 shadow-lg mb-12">
            Book a call
          </button>

          {/* Supporting images */}
          <div className="flex justify-center space-x-6">
            <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden">
              <img
                src="/src/assets/hero1.png"
                alt="Design sample 1"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden">
              <img
                src="/src/assets/hero.png"
                alt="Design sample 2"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="w-20 h-20 bg-gray-200 rounded-lg overflow-hidden">
              <img
                src="/src/assets/hero1.png"
                alt="Design sample 3"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">Nextspace</h3>
              <p className="text-gray-400 mb-4">
                Creating beautiful, functional spaces that inspire and transform lives through innovative interior design and architecture.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Home</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Projects</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <ul className="space-y-2 text-gray-400">
                <li>123 Design Street</li>
                <li>New York, NY 10001</li>
                <li>+1 (555) 123-4567</li>
                <li><EMAIL></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Nextspace. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;